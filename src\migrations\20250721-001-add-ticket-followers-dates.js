"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add followers field to store array of user IDs who can view ticket progress
    await queryInterface.addColumn("mo_support_tickets", "followers", {
      type: Sequelize.JSON,
      allowNull: true,
      comment: "Array of user IDs who can view and track ticket progress",
    });

    // Add start_date field for when work on ticket begins
    await queryInterface.addColumn("mo_support_tickets", "start_date", {
      type: Sequelize.DATE,
      allowNull: true,
      comment: "Date when work on the ticket started",
    });

    // Add due_date field for ticket completion deadline
    await queryInterface.addColumn("mo_support_tickets", "due_date", {
      type: Sequelize.DATE,
      allowNull: true,
      comment: "Date when ticket should be completed",
    });

    // Add index for followers field for better query performance
    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["followers"],
      name: "idx_tickets_followers",
      type: "GIN", // Use GIN index for JSON fields in PostgreSQL, or regular index for MySQL
    });

    // Add indexes for date fields
    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["start_date"],
      name: "idx_tickets_start_date",
    });

    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["due_date"],
      name: "idx_tickets_due_date",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_followers");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_start_date");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_due_date");

    // Remove columns
    await queryInterface.removeColumn("mo_support_tickets", "followers");
    await queryInterface.removeColumn("mo_support_tickets", "start_date");
    await queryInterface.removeColumn("mo_support_tickets", "due_date");
  },
};
