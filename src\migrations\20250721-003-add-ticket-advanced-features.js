"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add followers field to store array of user IDs who can view ticket progress
    await queryInterface.addColumn("mo_support_tickets", "followers", {
      type: Sequelize.JSON,
      allowNull: true,
      comment: "Array of user IDs who can view and track ticket progress",
    });

    // Add SLA pause functionality for festivals/holidays
    await queryInterface.addColumn("mo_support_tickets", "sla_paused", {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: "Whether SLA is currently paused (e.g., during festivals)",
    });

    await queryInterface.addColumn("mo_support_tickets", "sla_pause_reason", {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: "Reason for SLA pause (e.g., 'Festival Holiday', 'System Maintenance')",
    });

    // Add manual date overrides (agent-controlled)
    await queryInterface.addColumn("mo_support_tickets", "manual_start_date", {
      type: Sequelize.DATE,
      allowNull: true,
      comment: "Agent-set start date (overrides automatic calculation)",
    });

    await queryInterface.addColumn("mo_support_tickets", "manual_due_date", {
      type: Sequelize.DATE,
      allowNull: true,
      comment: "Agent-set due date (overrides SLA calculation)",
    });

    await queryInterface.addColumn("mo_support_tickets", "date_override_reason", {
      type: Sequelize.STRING(255),
      allowNull: true,
      comment: "Reason for manual date override",
    });

    await queryInterface.addColumn("mo_support_tickets", "date_override_by_user_id", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: "nv_users",
        key: "id",
      },
      comment: "User who set the manual dates",
    });

    // Add indexes for better query performance
    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["followers"],
      name: "idx_tickets_followers",
      type: "GIN", // Use GIN index for JSON fields in PostgreSQL, or regular index for MySQL
    });

    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["sla_paused"],
      name: "idx_tickets_sla_paused",
    });

    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["manual_start_date"],
      name: "idx_tickets_manual_start_date",
    });

    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["manual_due_date"],
      name: "idx_tickets_manual_due_date",
    });

    await queryInterface.addIndex("mo_support_tickets", {
      fields: ["date_override_by_user_id"],
      name: "idx_tickets_date_override_by_user",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_followers");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_sla_paused");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_manual_start_date");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_manual_due_date");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_date_override_by_user");

    // Remove columns
    await queryInterface.removeColumn("mo_support_tickets", "followers");
    await queryInterface.removeColumn("mo_support_tickets", "sla_paused");
    await queryInterface.removeColumn("mo_support_tickets", "sla_pause_reason");
    await queryInterface.removeColumn("mo_support_tickets", "manual_start_date");
    await queryInterface.removeColumn("mo_support_tickets", "manual_due_date");
    await queryInterface.removeColumn("mo_support_tickets", "date_override_reason");
    await queryInterface.removeColumn("mo_support_tickets", "date_override_by_user_id");
  },
};
