import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import {
  TICKET_MODULE,
  TICKET_TYPE,
  TICKET_PRIORITY,
  TICKET_STATUS,
} from "../helper/constant";

interface TicketAttributes {
  id?: number;
  ticket_slug?: string;
  organization_id: string;

  ticket_owner_user_id: number;

  ticket_title: string;
  ticket_description: string;
  ticket_module: keyof typeof TICKET_MODULE;
  ticket_type: keyof typeof TICKET_TYPE;
  ticket_priority: keyof typeof TICKET_PRIORITY;
  ticket_status: keyof typeof TICKET_STATUS;

  // Assignment tracking
  assigned_to_user_id?: number;
  assigned_at?: Date;
  assigned_by_user_id?: number;

  // Resolution tracking
  resolution_note?: string;
  resolved_at?: Date;
  resolved_by_user_id?: number;

  // Rating and review
  rating?: number;
  review_comment?: string;
  rated_at?: Date;
  rated_by_user_id?: number;

  // SLA tracking
  sla_due_date?: Date;
  first_response_at?: Date;
  sla_paused?: boolean;
  sla_pause_reason?: string;

  // Manual date overrides (agent-controlled)
  manual_start_date?: Date;
  manual_due_date?: Date;
  date_override_reason?: string;
  date_override_by_user_id?: number;

  // Followers for ticket tracking
  followers?: number[];

  // Audit fields
  created_by_user_id: number;
  updated_by_user_id?: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
}

export default class Ticket
  extends Model<TicketAttributes>
  implements TicketAttributes
{
  public id!: number;
  public ticket_slug!: string;
  public organization_id!: string;

  // User identification - only store user_id
  public ticket_owner_user_id!: number;

  public ticket_title!: string;
  public ticket_description!: string;
  public ticket_module!: keyof typeof TICKET_MODULE;
  public ticket_type!: keyof typeof TICKET_TYPE;
  public ticket_priority!: keyof typeof TICKET_PRIORITY;
  public ticket_status!: keyof typeof TICKET_STATUS;

  // Assignment tracking
  public assigned_to_user_id?: number;
  public assigned_at?: Date;
  public assigned_by_user_id?: number;

  // Resolution tracking
  public resolution_note?: string;
  public resolved_at?: Date;
  public resolved_by_user_id?: number;

  // Rating and review
  public rating?: number;
  public review_comment?: string;
  public rated_at?: Date;
  public rated_by_user_id?: number;

  // SLA tracking
  public sla_due_date?: Date;
  public first_response_at?: Date;
  public sla_paused?: boolean;
  public sla_pause_reason?: string;

  // Manual date overrides (agent-controlled)
  public manual_start_date?: Date;
  public manual_due_date?: Date;
  public date_override_reason?: string;
  public date_override_by_user_id?: number;

  // Followers for ticket tracking
  public followers?: number[];

  // Audit fields
  public created_by_user_id!: number;
  public updated_by_user_id?: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
  public readonly deleted_at?: Date;

  static associate(models: any) {
    // Ticket has many attachments
    Ticket.hasMany(models.TicketAttachment, {
      foreignKey: "ticket_id",
      as: "attachments",
    });

    // Ticket has many messages
    Ticket.hasMany(models.TicketMessage, {
      foreignKey: "ticket_id",
      as: "messages",
    });

    // Ticket has many history records
    Ticket.hasMany(models.TicketHistory, {
      foreignKey: "ticket_id",
      as: "history",
    });
  }
}

Ticket.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    ticket_slug: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },
    organization_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    ticket_owner_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "nv_users",
        key: "id",
      },
    },
    ticket_title: {
      type: DataTypes.STRING(200),
      allowNull: false,
    },
    ticket_description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ticket_module: {
      type: DataTypes.ENUM(...Object.values(TICKET_MODULE)),
      allowNull: false,
      defaultValue: TICKET_MODULE.OTHER,
    },
    ticket_type: {
      type: DataTypes.ENUM(...Object.values(TICKET_TYPE)),
      allowNull: false,
      defaultValue: TICKET_TYPE.GENERAL_INQUIRY,
    },
    ticket_priority: {
      type: DataTypes.ENUM(...Object.values(TICKET_PRIORITY)),
      allowNull: false,
      defaultValue: TICKET_PRIORITY.MEDIUM,
    },
    ticket_status: {
      type: DataTypes.ENUM(...Object.values(TICKET_STATUS)),
      allowNull: false,
      defaultValue: TICKET_STATUS.OPEN,
    },
    assigned_to_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_users",
        key: "id",
      },
    },
    assigned_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    assigned_by_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_users",
        key: "id",
      },
    },
    resolution_note: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    resolved_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    resolved_by_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_users",
        key: "id",
      },
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5,
      },
    },
    review_comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    rated_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    rated_by_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_users",
        key: "id",
      },
    },
    sla_due_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    first_response_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    sla_paused: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      comment: "Whether SLA is currently paused (e.g., during festivals)",
    },
    sla_pause_reason: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment:
        "Reason for SLA pause (e.g., 'Festival Holiday', 'System Maintenance')",
    },
    // Manual date overrides (agent-controlled)
    manual_start_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "Agent-set start date (overrides automatic calculation)",
    },
    manual_due_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "Agent-set due date (overrides SLA calculation)",
    },
    date_override_reason: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "Reason for manual date override",
    },
    date_override_by_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_users",
        key: "id",
      },
      comment: "User who set the manual dates",
    },
    // Followers for ticket tracking
    followers: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: "Array of user IDs who can view and track ticket progress",
    },
    created_by_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "nv_users",
        key: "id",
      },
    },
    updated_by_user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_users",
        key: "id",
      },
    },
  },
  {
    sequelize,
    tableName: "mo_support_tickets",
    modelName: "Ticket",
    timestamps: true,
    paranoid: true,
    underscored: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    deletedAt: "deleted_at",
  }
);
