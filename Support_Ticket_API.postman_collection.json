{"info": {"_postman_id": "2e642f0e-cc51-45af-ae76-6c839b5a6a5e", "name": "🎫 Support Ticket API's - Updated & Accurate", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Complete Support Ticket Microservice API collection with 100% accurate parameters and enum values"}, "item": [{"name": "🏥 Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Check if the support ticket microservice is running and get available routes"}, "response": []}]}, {"name": "🎫 Support Tickets", "item": [{"name": "Get All Tickets (with filters)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/list?page=1&limit=10&organization_id={{organization_id}}&ticket_status=open&ticket_priority=high&ticket_module=hrms&ticket_type=bug&assigned_to_user_id=123&search=test&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Optional organization ID filter. If omitted, non-default users receive their own organization's tickets, while default/super-admin users receive tickets from all organizations."}, {"key": "ticket_status", "value": "open", "description": "Filter by status: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed"}, {"key": "ticket_priority", "value": "high", "description": "Filter by priority: low, medium, high, urgent"}, {"key": "ticket_module", "value": "hrms", "description": "Filter by module: hrms, pms, other"}, {"key": "ticket_type", "value": "bug_report", "description": "Filter by type: bug_report, feature_request, general_inquiry, technical_issue, non_technical_issue, export_help, account_issue, billing, performance, notifications, support"}, {"key": "assigned_to_user_id", "value": "123", "description": "Filter by assigned user ID"}, {"key": "search", "value": "test", "description": "Search in ticket title and description"}, {"key": "sort_by", "value": "created_at", "description": "Sort by field"}, {"key": "sort_order", "value": "DESC", "description": "Sort order: ASC, DESC"}]}, "description": "Get all support tickets with flexible filtering and pagination. Organization behaviour: (a) non-default users → their own org when org_id omitted; (b) default/super-admin → all orgs when org_id omitted; pass organization_id to narrow explicitly."}, "response": []}, {"name": "Get Single Ticket", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}"]}, "description": "Get a single ticket by ID"}, "response": []}, {"name": "Create Ticket (with file upload)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ticket_title", "value": "Unable to export recipe data", "description": "Required: 5-200 characters", "type": "text"}, {"key": "ticket_description", "value": "The export button in the recipe module is not responding when clicked. This is affecting our daily operations as we cannot export recipe data for our reports.", "description": "Required: 10-5000 characters", "type": "text"}, {"key": "ticket_module", "value": "hrms", "description": "Required: hrms, pms, other (default: other)", "type": "text"}, {"key": "ticket_type", "value": "bug_report", "description": "Required: bug_report, feature_request, general_inquiry, technical_issue, non_technical_issue, export_help, account_issue, billing, performance, notifications, support (default: general_inquiry)", "type": "text"}, {"key": "ticket_priority", "value": "high", "description": "Required: low, medium, high, urgent (default: medium)", "type": "text"}, {"key": "support_pin", "value": "{{support_pin}}", "description": "Required: 1-50 characters. Organization support PIN for validation", "type": "text"}, {"key": "ticketFiles", "description": "Optional: Upload up to 5 files, max 50 MB each", "type": "file", "disabled": true}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/create", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "create"]}, "description": "Create a new support ticket with optional file attachments. Support PIN is required for validation."}, "response": []}, {"name": "Update or Assign Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ticket_title", "value": "Updated: Unable to export recipe data", "description": "Optional: 5-200 characters", "type": "text"}, {"key": "ticket_description", "value": "Updated description with more details about the export issue", "description": "Optional: 10-5000 characters", "type": "text"}, {"key": "ticket_module", "value": "hrms", "description": "Optional: hrms, pms, other", "type": "text"}, {"key": "ticket_type", "value": "bug_report", "description": "Optional: bug_report, feature_request, general_inquiry, technical_issue, non_technical_issue, export_help, account_issue, billing, performance, notifications, support", "type": "text"}, {"key": "ticket_priority", "value": "urgent", "description": "Optional: low, medium, high, urgent", "type": "text"}, {"key": "ticket_status", "value": "in_progress", "description": "Optional: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed", "type": "text"}, {"key": "assigned_to_user_id", "value": "456", "description": "Optional: User ID to assign ticket to (positive integer)", "type": "text"}, {"key": "resolution_note", "value": "Working on the export functionality fix. This issue has been identified and a patch is being developed.", "description": "Optional: Resolution note (10-2000 characters)", "type": "text"}, {"key": "rating", "value": "4", "description": "Optional: Rating from 1-5 (integer)", "type": "text"}, {"key": "review_comment", "value": "Good response time and helpful support", "description": "Optional: Review comment (max 1000 characters)", "type": "text"}, {"key": "support_pin", "value": "{{support_pin}}", "description": "Optional: Support PIN for verification", "type": "text"}, {"key": "followers", "value": "[1, 2, 3]", "description": "Optional: Array of user IDs who can track ticket progress (JSON format)", "type": "text"}, {"key": "manual_start_date", "value": "2025-01-21T09:00:00Z", "description": "Optional: Agent-set start date (ISO format) - Agent/Admin only", "type": "text"}, {"key": "manual_due_date", "value": "2025-01-25T17:00:00Z", "description": "Optional: Agent-set due date (ISO format) - Agent/Admin only", "type": "text"}, {"key": "date_override_reason", "value": "Client requested specific timeline", "description": "Optional: Reason for manual date override", "type": "text"}, {"key": "sla_paused", "value": "false", "description": "Optional: Pause SLA during festivals/holidays - Agent/Admin only", "type": "text"}, {"key": "sla_pause_reason", "value": "Festival Holiday", "description": "Optional: Reason for SLA pause", "type": "text"}, {"key": "ticketFiles", "description": "Optional: Upload up to 5 files, max 50MB each", "type": "file", "disabled": true}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/update/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "update", "{{ticket_id}}"]}, "description": "Update ticket details with advanced features: PIN verification, followers management, manual date overrides, SLA pause functionality. Supports file uploads and i18n messages (EN/FR/DE). Agent/Admin features: manual_start_date, manual_due_date, sla_paused."}, "response": []}, {"name": "Delete Ticket", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/delete/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "delete", "{{ticket_id}}"]}, "description": "Delete a support ticket"}, "response": []}, {"name": "Resolve Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"resolution_note\": \"The export functionality has been fixed. The issue was caused by a missing database index which has now been added. Please try the export feature again.\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/resolve/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "resolve", "{{ticket_id}}"]}, "description": "Resolve a ticket with resolution note (10-2000 characters required)"}, "response": []}, {"name": "Rate Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"review_comment\": \"Excellent support! The issue was resolved quickly and the explanation was clear.\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/rate/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "rate", "{{ticket_id}}"]}, "description": "Rate a ticket (1-5 stars required, review comment optional, max 1000 characters)"}, "response": []}, {"name": "Get Ticket Comments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}/comments?page=1&limit=10", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}", "comments"], "query": [{"key": "page", "value": "1", "description": "Optional: Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Optional: Comments per page (default: 10, max: 100)"}]}, "description": "Get all comments/additional notes attached to a ticket. These are different from communication messages - they are additional information added when users can't edit the original ticket."}, "response": []}, {"name": "Add Ticket Comment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "comment_text", "value": "Additional information: I forgot to mention that this issue only occurs when using Chrome browser. It works fine in Firefox and Safari.", "description": "Required: 1-5000 characters. Additional information or notes to add to the ticket.", "type": "text"}, {"key": "is_private", "value": "false", "description": "Optional: Set to true for internal comments (agents/admins only), default: false", "type": "text"}, {"key": "attachment", "description": "Optional: Upload file with the comment (max 1 file, 50MB)", "type": "file", "disabled": true}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}/comments", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}", "comments"]}, "description": "Add additional information/notes to a ticket when you can't edit the original ticket details. This is different from communication messages - it's for adding extra information to the ticket itself."}, "response": []}, {"name": "Get Ticket History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}/history?page=1&limit=10", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}", "history"], "query": [{"key": "page", "value": "1", "description": "Optional: Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Optional: History per page (default: 10, max: 100)"}]}, "description": "Get the history of changes and updates made to a specific ticket, including status, priority, assigned user, and resolution notes."}, "response": []}]}, {"name": "💬 Support Messages", "item": [{"name": "Get Ticket Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/messages/ticket/{{ticket_id}}?include_private=false&page=1&limit=20", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "ticket", "{{ticket_id}}"], "query": [{"key": "include_private", "value": "false", "description": "Include internal comments (agents/admins only can set to true)"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Messages per page (default: 20, max: 100)"}]}, "description": "Get all messages for a specific ticket with pagination and visibility filtering"}, "response": []}, {"name": "Add Ticket Message (with file upload)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_text", "value": "Thank you for reporting this issue. I have escalated this to our development team and they are working on a fix. We will update you as soon as we have more information.", "description": "Required: 1-5000 characters", "type": "text"}, {"key": "is_private", "value": "false", "description": "Optional: Set to true for internal comments (agents/admins only), default: false", "type": "text"}, {"key": "attachment", "description": "Optional: Upload file with the message (max 1 file, 50MB)", "type": "file", "disabled": true}]}, "url": {"raw": "{{base_url}}/v1/private/messages/ticket/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "ticket", "{{ticket_id}}"]}, "description": "Add a message/comment to a ticket with optional file attachments"}, "response": []}]}, {"name": "⚙️ Support Configuration", "item": [{"name": "Get Support Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}"]}, "description": "Get support configuration for an organization"}, "response": []}, {"name": "Create/Update Support Config", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"support_pin\": \"{{support_pin}}\",\n  \"is_active\": true,\n  \"allow_attachments\": true,\n  \"max_attachment_size\": 52428800,\n  \"allowed_file_types\": [\"pdf\", \"png\", \"jpg\", \"jpeg\", \"doc\", \"docx\"],\n  \"auto_assignment_enabled\": false,\n  \"sla_response_time_hours\": 24,\n  \"sla_resolution_time_hours\": 72,\n  \"sla_low_priority_hours\": 120,\n  \"sla_medium_priority_hours\": 72,\n  \"sla_high_priority_hours\": 48,\n  \"sla_urgent_priority_hours\": 24\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}"]}, "description": "Create or update support configuration for an organization. Includes priority-specific SLA times: sla_low_priority_hours (120h), sla_medium_priority_hours (72h), sla_high_priority_hours (48h), sla_urgent_priority_hours (24h)"}, "response": []}, {"name": "Validate Support PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"support_pin\": \"{{support_pin}}\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/config/{{organization_id}}/validate-pin", "host": ["{{base_url}}"], "path": ["v1", "public", "config", "{{organization_id}}", "validate-pin"]}, "description": "Validate support PIN for an organization (public endpoint, no authentication required)"}, "response": []}, {"name": "Get Default Support Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/default", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "default"]}, "description": "Get the default support configuration for new organizations"}, "response": []}, {"name": "Get All Support Configs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config", "host": ["{{base_url}}"], "path": ["v1", "private", "config"]}, "description": "Get a list of all existing support configurations"}, "response": []}, {"name": "Create/Update Default Config", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"is_active\": true,\n  \"allow_attachments\": true,\n  \"max_attachment_size\": 5242880,\n  \"allowed_file_types\": [\"pdf\", \"png\", \"jpg\", \"jpeg\", \"doc\", \"docx\"],\n  \"auto_assignment_enabled\": false,\n  \"sla_response_time_hours\": 24,\n  \"sla_resolution_time_hours\": 72,\n  \"sla_low_priority_hours\": 120,\n  \"sla_medium_priority_hours\": 72,\n  \"sla_high_priority_hours\": 48,\n  \"sla_urgent_priority_hours\": 24\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/default", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "default"]}, "description": "Create or update default support configuration with priority-specific SLA times (Admin only)"}, "response": []}]}, {"name": "👤 Admin Dashboard & Management", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/dashboard?organization_id={{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "dashboard"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}]}, "description": "Get admin dashboard overview with statistics, metrics, and recent activity"}, "response": []}, {"name": "Admin Tickets with Advanced Filters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/tickets?page=1&limit=10&search=export&status=open&priority=high&module_type=hrms&issue_type=bug&assigned_to_user_id=null&organization_id={{organization_id}}&date_from=2024-01-01&date_to=2024-12-31&overdue=true&unassigned=true&has_rating=false&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "search", "value": "export", "description": "Search in ticket slug, title, description"}, {"key": "status", "value": "open", "description": "Filter by status: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed"}, {"key": "priority", "value": "high", "description": "Filter by priority: low, medium, high, urgent"}, {"key": "module_type", "value": "hrms", "description": "Filter by module type: hrms, pms, other"}, {"key": "issue_type", "value": "bug_report", "description": "Filter by issue type: bug_report, feature_request, general_inquiry, technical_issue, non_technical_issue, export_help, account_issue, billing, performance, notifications, support"}, {"key": "assigned_to_user_id", "value": "null", "description": "Filter by assigned user (use 'null' for unassigned)"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}, {"key": "date_from", "value": "2024-01-01", "description": "Filter tickets created from this date"}, {"key": "date_to", "value": "2024-12-31", "description": "Filter tickets created until this date"}, {"key": "overdue", "value": "true", "description": "Filter overdue tickets only"}, {"key": "unassigned", "value": "true", "description": "Filter unassigned tickets only"}, {"key": "has_rating", "value": "false", "description": "Filter tickets with/without rating"}, {"key": "sort_by", "value": "created_at", "description": "Sort field"}, {"key": "sort_order", "value": "DESC", "description": "Sort order: ASC, DESC"}]}, "description": "Retrieve tickets with comprehensive filtering and search capabilities for admin users"}, "response": []}, {"name": "Bulk Ticket Operations", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3, 4, 5],\n  \"operation\": \"assign\",\n  \"data\": {\n    \"assigned_to_user_id\": 123,\n    \"change_note\": \"Bulk assignment to support team lead\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}, "description": "Execute bulk operations on multiple tickets: assign, status_update, priority_update, delete"}, "response": []}, {"name": "Get Analytics and Reports", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/analytics?organization_id={{organization_id}}&period=30d&date_from=2024-01-01&date_to=2024-12-31", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "analytics"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}, {"key": "period", "value": "30d", "description": "Time period: 7d, 30d, 90d, 1y (default: 30d)"}, {"key": "date_from", "value": "2024-01-01", "description": "Custom start date (overrides period)"}, {"key": "date_to", "value": "2024-12-31", "description": "Custom end date (overrides period)"}]}, "description": "Get comprehensive analytics including ticket trends, performance metrics, SLA compliance, and customer satisfaction"}, "response": []}, {"name": "Get Organization Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/org-users", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "org-users"]}, "description": "Retrieve active users for the configured organization (env ORGANIZATION_ID) excluding Super Admin accounts."}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost:5008", "type": "string", "description": "Base URL for the Support Ticket Microservice"}, {"key": "auth_token", "value": "your-jwt-token-here", "type": "string", "description": "JWT authentication token for API access"}, {"key": "organization_id", "value": "b7ccd39a-23e9-49e6-8831-a3597a335bb1", "type": "string", "description": "Organization ID for filtering and access control"}, {"key": "ticket_id", "value": "1", "type": "string", "description": "Ticket ID for specific ticket operations"}, {"key": "support_pin", "value": "0965", "type": "string", "description": "Support PIN for ticket creation (tested and working)"}]}