import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { db } from "../models";
import { getPagination, getAssignableUsers, getUser } from "../utils/common";
import { TICKET_STATUS, MESSAGE_TYPE } from "../helper/constant";
import {
  getTicketByIdRaw,
  getTicketBySlugRaw,
  getTicketsListRaw,
  generateTicketSlug,
  calculateSlaDueDate,
} from "../helper/ticket.helper";
import {
  isDefaultAccess,
  hasFullTicketAccess,
  validateUserExists,
  isAgent,
} from "../utils/common";
import { organizationHelper } from "../helper/organization.helper";
import { recordTicketHistory } from "../helper/history.helper";

const Ticket = db.Ticket;
const TicketMessage = db.TicketMessage;
const TicketAttachment = db.TicketAttachment;
const TicketHistory = db.TicketHistory;

/**
 * Get all support tickets with pagination and filters (following recipe-ms pattern)
 */
const getAllTickets = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page,
      limit,
      search,
      ticket_status,
      ticket_priority,
      ticket_type,
      ticket_module,
      assigned_to_user_id,
      sort_by,
      sort_order,
      organization_id,
    } = req.query;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Decide organization filter based on query and user role
    let orgFilter: string | undefined = undefined;

    if (organization_id) {
      // Organization filter explicitly provided
      if (hasFullAccess) {
        orgFilter = organization_id as string;
      } else {
        // Regular users can only retrieve their own organization tickets
        orgFilter = organizationId;
      }
    } else {
      // No organization filter provided
      if (!hasFullAccess) {
        orgFilter = organizationId; // restrict to own org
      }
      // For users with full access (admin or agent org), orgFilter remains undefined to fetch all
    }

    // Use raw query for better performance (following recipe-ms pattern)
    const { limit: queryLimit, offset } = getPagination(
      page as string,
      limit as string
    );

    const { tickets, total } = await getTicketsListRaw(orgFilter, {
      limit: queryLimit,
      offset,
      search: search as string,
      ticket_status: ticket_status as string,
      ticket_priority: ticket_priority as string,
      ticket_type: ticket_type as string,
      ticket_module: ticket_module as string,
      assigned_to_user_id: assigned_to_user_id
        ? Number(assigned_to_user_id)
        : undefined,
      sort_by: sort_by as string,
      sort_order: sort_order as string,
    });

    if (!tickets || tickets.length === 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("NO_TICKETS_FOUND") || "No tickets found",
        data: [],
        ...(limit && {
          pagination: {
            total,
            page: Number(page) || 1,
            limit: Number(limit),
            totalPages: Math.ceil(total / Number(limit)),
          },
        }),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKETS_RETRIEVED_SUCCESSFULLY") ||
        "Tickets retrieved successfully",
      data: tickets,
      ...(limit && {
        pagination: {
          total,
          page: Number(page) || 1,
          limit: Number(limit),
          totalPages: Math.ceil(total / Number(limit)),
        },
      }),
    });
  } catch (error: unknown) {
    console.error("Error in getAllTickets:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get single ticket by ID or slug (following recipe-ms pattern)
 */
const getTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    let ticketData;
    const isNumeric = !isNaN(Number(id));

    // For users with full access (admin or agent org), don't filter by organization
    const orgFilter = hasFullAccess ? null : organizationId;

    if (isNumeric) {
      ticketData = await getTicketByIdRaw(Number(id), orgFilter);
    } else {
      ticketData = await getTicketBySlugRaw(id, orgFilter);
    }

    if (!ticketData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RETRIEVED_SUCCESSFULLY") ||
        "Ticket retrieved successfully",
      data: ticketData,
    });
  } catch (error: unknown) {
    console.error("Error in getTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Create new support ticket (following recipe-ms pattern)
 */
const createTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      ticket_title,
      ticket_description,
      ticket_module,
      ticket_type,
      ticket_priority,
      support_pin,
    } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const isSuperAdminUser = await isDefaultAccess(userId);

    if (!userId || (!organizationId && !isSuperAdminUser)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Validate support PIN before creating ticket (using database query instead of Keycloak API)
    const pinValidation = await organizationHelper.validateSupportPinFromDB(
      organizationId,
      support_pin
    );

    if (!pinValidation.isValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("INVALID_SUPPORT_PIN") || "Invalid support PIN",
        error: pinValidation.error,
      });
    }

    // Generate unique ticket slug
    const ticketSlug = await generateTicketSlug(organizationId);

    // Fetch organization config for SLA parameters
    const orgConfig = await db.SupportConfig.findOne({
      where: { organization_id: organizationId },
    });

    // Calculate SLA due date based on priority/config
    const slaDueDate = calculateSlaDueDate(
      new Date(),
      ticket_priority,
      orgConfig
    );

    const ticketData = {
      ticket_slug: ticketSlug,
      ticket_title,
      ticket_description,
      ticket_module,
      ticket_type,
      ticket_priority,
      ticket_status: TICKET_STATUS.OPEN,
      organization_id: organizationId,
      ticket_owner_user_id: userId,
      created_by_user_id: userId,
      updated_by_user_id: userId,
      sla_due_date: slaDueDate,
    };

    const ticket = await Ticket.create(ticketData);

    // Handle file uploads exactly like recipe microservice
    const uploadedAttachments: any[] = [];
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle ticket files upload
      if (files.ticketFiles && files.ticketFiles.length > 0) {
        const { TicketAttachment } = db;

        for (const file of files.ticketFiles) {
          // Determine attachment type based on MIME type
          let attachmentType = "document";
          if (file.mimetype.startsWith("image/")) {
            attachmentType = "image";
          } else if (file.mimetype.startsWith("video/")) {
            attachmentType = "video";
          } else if (file.mimetype.startsWith("audio/")) {
            attachmentType = "audio";
          }

          // Create attachment record matching the model structure
          const attachmentData = {
            ticket_id: ticket.id,
            item_id: file.item_id,
            attachment_type: attachmentType,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            created_by: userId,
          };

          const attachment = await TicketAttachment.create(attachmentData);
          uploadedAttachments.push({
            id: attachment.id,
            item_id: file.item_id,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            attachment_type: attachmentType,
          });
        }
      }
    }

    // Include attachments in response if any were uploaded
    const responseData = {
      ...ticket.toJSON(),
      attachments:
        uploadedAttachments.length > 0 ? uploadedAttachments : undefined,
    };

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message:
        res.__("TICKET_CREATED_SUCCESSFULLY") || "Ticket created successfully",
      data: responseData,
    });
  } catch (error: unknown) {
    console.error("Error in createTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Update support ticket (following recipe-ms pattern)
 */
const updateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // PIN verification if provided (similar to create ticket)
    if (updateData.support_pin) {
      const pinValidation = await organizationHelper.validateSupportPinFromDB(
        organizationId,
        updateData.support_pin
      );

      if (!pinValidation.isValid) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          status: false,
          message: res.__("INVALID_SUPPORT_PIN") || "Invalid support PIN",
          error: pinValidation.error,
        });
      }
      // Remove PIN from update data as it's not a ticket field
      delete updateData.support_pin;
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Add audit fields
    updateData.updated_by_user_id = userId;

    // Check if assignment change is requested
    const assignmentChanged =
      updateData.assigned_to_user_id &&
      updateData.assigned_to_user_id !== ticket.assigned_to_user_id;

    if (assignmentChanged) {
      // Validate assignee exists and is active
      const assigneeId = Number(updateData.assigned_to_user_id);
      if (!assigneeId || !(await validateUserExists(assigneeId))) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message:
            res.__("INVALID_ASSIGNEE") ||
            "Assignee user does not exist or is inactive",
        });
      }

      // Optional: Ensure the assignee belongs to same organization unless current user is default access
      if (!(await isDefaultAccess(userId))) {
        const assignee = await getUser(assigneeId);
        if (!assignee || assignee.organization_id !== organizationId) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message:
              res.__("ASSIGNEE_OUTSIDE_ORG") ||
              "Cannot assign ticket to a user outside your organization",
          });
        }
      }

      updateData.assigned_at = new Date();
      updateData.assigned_by_user_id = userId;
      // If status not explicitly set, default to ASSIGNED
      if (!updateData.ticket_status) {
        updateData.ticket_status = TICKET_STATUS.ASSIGNED;
      }
    }

    // Capture old values for history
    const oldData = ticket.toJSON();

    // If priority is changing, recalculate SLA due date (unless manual override exists)
    if (
      updateData.ticket_priority &&
      updateData.ticket_priority !== ticket.ticket_priority &&
      !ticket.manual_due_date // Don't override manual dates
    ) {
      const orgConfig = await db.SupportConfig.findOne({
        where: { organization_id: organizationId },
      });
      updateData.sla_due_date = calculateSlaDueDate(
        new Date(),
        updateData.ticket_priority,
        orgConfig
      );
    }

    // Handle manual date overrides (agent/admin only)
    if (updateData.manual_start_date || updateData.manual_due_date) {
      if (!hasFullAccess && !(await isAgent(userId))) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message:
            res.__("INSUFFICIENT_PERMISSIONS") ||
            "Only agents can set manual dates",
        });
      }

      // Set override metadata
      if (updateData.manual_start_date || updateData.manual_due_date) {
        updateData.date_override_by_user_id = userId;
        if (!updateData.date_override_reason) {
          updateData.date_override_reason = "Manual date override by agent";
        }
      }
    }

    // Handle followers (validate user IDs exist)
    if (updateData.followers && Array.isArray(updateData.followers)) {
      for (const followerId of updateData.followers) {
        const followerExists = await validateUserExists(followerId);
        if (!followerExists) {
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message:
              res.__("INVALID_FOLLOWER") ||
              `User ID ${followerId} does not exist`,
          });
        }
      }
    }

    // Handle SLA pause (agent/admin only)
    if (updateData.sla_paused !== undefined) {
      if (!hasFullAccess && !(await isAgent(userId))) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message:
            res.__("INSUFFICIENT_PERMISSIONS") || "Only agents can pause SLA",
        });
      }

      if (updateData.sla_paused && !updateData.sla_pause_reason) {
        updateData.sla_pause_reason = "SLA paused by agent";
      }
    }

    await ticket.update(updateData);

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    const uploadedAttachments: any[] = [];
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle ticket files upload
      if (files.ticketFiles && files.ticketFiles.length > 0) {
        const { TicketAttachment } = db;

        for (const file of files.ticketFiles) {
          // Determine attachment type based on MIME type
          let attachmentType = "document";
          if (file.mimetype.startsWith("image/")) {
            attachmentType = "image";
          } else if (file.mimetype.startsWith("video/")) {
            attachmentType = "video";
          } else if (file.mimetype.startsWith("audio/")) {
            attachmentType = "audio";
          }

          // Create attachment record matching the model structure
          const attachmentData = {
            ticket_id: ticket.id,
            item_id: file.item_id,
            attachment_type: attachmentType,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            created_by: userId,
          };

          const attachment = await TicketAttachment.create(attachmentData);
          uploadedAttachments.push({
            id: attachment.id,
            item_id: file.item_id,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            attachment_type: attachmentType,
          });
        }
      }
    }

    const responseData = {
      ...ticket.toJSON(),
      attachments:
        uploadedAttachments.length > 0 ? uploadedAttachments : undefined,
    };

    // Determine user-friendly message based on what was updated
    let userMessage = "Your support ticket has been updated successfully";
    if (assignmentChanged) {
      userMessage = "Your ticket has been assigned to our support team";
    } else if (updateData.followers) {
      userMessage =
        "Team members have been added to track your ticket progress";
    } else if (updateData.manual_start_date || updateData.manual_due_date) {
      userMessage = "Timeline has been updated for your ticket";
    } else if (updateData.sla_paused) {
      userMessage =
        "Ticket timeline has been paused due to holidays/maintenance";
    } else if (
      updateData.ticket_priority &&
      updateData.ticket_priority !== oldData.ticket_priority
    ) {
      userMessage = "Your ticket priority has been updated";
    } else if (
      updateData.ticket_status &&
      updateData.ticket_status !== oldData.ticket_status
    ) {
      userMessage = "Your ticket status has been updated";
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: userMessage,
      data: responseData,
      user_friendly_message: userMessage,
    });
  } catch (error: any) {
    console.error("Error in updateTicket:", error);
    // Handle foreign key errors gracefully
    if (error?.name === "SequelizeForeignKeyConstraintError") {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          res.__("INVALID_ASSIGNEE") ||
          "Assignee user does not exist or is inactive",
      });
    }
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Delete support ticket (following recipe-ms pattern)
 */
const deleteTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Hard delete: remove ticket and its related data permanently
    // Delete child records first to maintain referential integrity
    await TicketMessage.destroy({
      where: { ticket_id: ticket.id },
      force: true,
    });
    await TicketAttachment.destroy({
      where: { ticket_id: ticket.id },
      force: true,
    });
    await TicketHistory.destroy({
      where: { ticket_id: ticket.id },
      force: true,
    });

    // Finally, delete the ticket itself
    await ticket.destroy({ force: true });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_DELETED_SUCCESSFULLY") || "Ticket deleted successfully",
    });
  } catch (error: unknown) {
    console.error("Error in deleteTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Assign ticket to user (following recipe-ms pattern)
 */
const assignTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { assigned_to_user_id } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    const oldData = ticket.toJSON();

    await ticket.update({
      assigned_to_user_id: assigned_to_user_id,
      assigned_at: new Date(),
      assigned_by_user_id: userId,
      ticket_status: TICKET_STATUS.ASSIGNED,
      updated_by_user_id: userId,
    });

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    // Re-fetch with joins for full info
    const orgFilter = hasFullAccess ? null : organizationId;
    const enrichedTicket = await getTicketByIdRaw(ticket.id, orgFilter);

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_ASSIGNED_SUCCESSFULLY") ||
        "Ticket assigned successfully",
      data: enrichedTicket,
    });
  } catch (error: unknown) {
    console.error("Error in assignTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Resolve ticket (following recipe-ms pattern)
 */
const resolveTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { resolution_note } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    const oldData = ticket.toJSON();

    await ticket.update({
      ticket_status: TICKET_STATUS.RESOLVED,
      resolution_note,
      resolved_at: new Date(),
      resolved_by_user_id: userId,
      updated_by_user_id: userId,
    });

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RESOLVED_SUCCESSFULLY") ||
        "Ticket resolved successfully",
      data: ticket,
    });
  } catch (error: unknown) {
    console.error("Error in resolveTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Rate ticket (following recipe-ms pattern)
 */
const rateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { rating, review_comment } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    const oldData = ticket.toJSON();

    await ticket.update({
      rating,
      review_comment,
      rated_at: new Date(),
      rated_by_user_id: userId,
      updated_by_user_id: userId,
    });

    await recordTicketHistory(oldData, ticket.toJSON(), userId);

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RATED_SUCCESSFULLY") || "Ticket rated successfully",
      data: ticket,
    });
  } catch (error: unknown) {
    console.error("Error in rateTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Add comment/note to ticket (for additional information)
 */
const addTicketComment = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { comment_text, is_private = false } = req.body;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Verify ticket exists and user has access
    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Handle file upload from S3 middleware (same as messages)
    let attachmentItemId = null;
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      if (files.attachment && files.attachment.length > 0) {
        const uploadedFile = files.attachment[0];
        attachmentItemId = uploadedFile.item_id;
      }
    }

    // Create ticket comment
    const comment = await TicketMessage.create({
      ticket_id: Number(id),
      message_text: comment_text,
      message_type: MESSAGE_TYPE.TICKET_COMMENT,
      is_private,
      attachment_id: attachmentItemId,
      created_by: userId,
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message:
        res.__("TICKET_COMMENT_ADDED") ||
        "Comment added to ticket successfully",
      data: comment,
    });
  } catch (error: unknown) {
    console.error("Error in addTicketComment:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get comments for a ticket (additional information)
 */
const getTicketComments = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { page, limit } = req.query;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Verify ticket exists and user has access
    let whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Get pagination parameters
    const pagination = getPagination(page as string, limit as string);
    const isPaginated = !!(page || limit);

    whereClause = {
      ticket_id: Number(id),
      message_type: MESSAGE_TYPE.TICKET_COMMENT,
    };

    // Build query options
    const queryOptions: any = {
      where: whereClause,
      order: [["created_at", "ASC"]],
    };

    // Only add pagination if parameters provided
    if (isPaginated && pagination.limit) {
      queryOptions.limit = pagination.limit;
      queryOptions.offset = pagination.offset;
    }

    const { rows: comments, count } =
      await TicketMessage.findAndCountAll(queryOptions);

    // Format response
    let responseData: any;
    if (isPaginated) {
      const totalPages = Math.ceil(count / pagination.limit!);
      responseData = {
        comments,
        pagination: {
          currentPage: parseInt(page as string) || 1,
          totalPages,
          totalItems: count,
          itemsPerPage: pagination.limit,
        },
      };
    } else {
      responseData = {
        comments,
        totalCount: count,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_COMMENTS_RETRIEVED") ||
        "Ticket comments retrieved successfully",
      data: responseData,
    });
  } catch (error: unknown) {
    console.error("Error in getTicketComments:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get history records for a ticket
 */
const getTicketHistory = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    const hasFullAccess = await hasFullTicketAccess(userId);

    if (!userId || (!organizationId && !hasFullAccess)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // ensure ticket belongs to same organization (unless admin or agent)
    const whereClause: any = {
      id: Number(id),
      deleted_at: null,
    };

    // For users with full access (admin or agent org), don't filter by organization
    if (!hasFullAccess) {
      whereClause.organization_id = organizationId;
    }

    const ticket = await Ticket.findOne({
      where: whereClause,
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    const history = await db.TicketHistory.findAll({
      where: { ticket_id: ticket.id },
      order: [["created_at", "ASC"]],
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("HISTORY_FETCHED_SUCCESSFULLY") ||
        "Ticket history retrieved successfully",
      data: history,
    });
  } catch (error) {
    console.error("Error in getTicketHistory:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message:
        res.__("HISTORY_FETCH_FAILED") || "Failed to retrieve ticket history",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get assignable users for ticket assignment
 */
const getAssignableUsersForTicket = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const organizationId = (req as any).user?.organization_id;
    const {
      search,
      roleFilter,
      departmentId,
      branchId,
      page,
      limit,
      includeInactive,
    } = req.query;

    const userId = (req as any).user?.id;
    const isSuperAdminUser = await isDefaultAccess(userId);

    if (!userId || (!organizationId && !isSuperAdminUser)) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const options = {
      search: search as string,
      roleFilter: roleFilter as string,
      departmentId: departmentId ? Number(departmentId) : undefined,
      branchId: branchId ? Number(branchId) : undefined,
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      includeInactive: includeInactive === "true",
    };

    const result = await getAssignableUsers(organizationId, options);

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("USERS_RETRIEVED_SUCCESSFULLY") ||
        "Users retrieved successfully",
      data: result,
    });
  } catch (error: unknown) {
    console.error("Error in getAssignableUsersForTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Export all controller functions (following recipe-ms pattern)
export default {
  getAllTickets,
  getTicket,
  createTicket,
  updateTicket,
  deleteTicket,
  resolveTicket,
  rateTicket,
  addTicketComment,
  getTicketComments,
  getTicketHistory,
};
