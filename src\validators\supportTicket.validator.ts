import { celebrate, Joi, Segments } from "celebrate";
import {
  TICKET_MODULE,
  TICKET_TYPE,
  TICKET_PRIORITY,
  TICKET_STATUS,
  SORT_ORDER,
  SORT_BY,
  VALIDATION_CONSTANT,
} from "../helper/constant";

// Following recipe-ms pattern with function wrapper
const createTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_TITLE_MIN)
          .max(VALIDATION_CONSTANT.TICKET_TITLE_MAX)
          .required(),
        ticket_description: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MIN)
          .max(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MAX)
          .required(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .required(),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .required(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .required(),
        support_pin: Joi.string().min(1).max(50).required(),
        // File upload fields are handled by multer middleware
        ticketFiles: Joi.any().optional(),
      })
      .unknown(true), // Allow unknown fields for file upload
  });

const updateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_TITLE_MIN)
          .max(VALIDATION_CONSTANT.TICKET_TITLE_MAX)
          .optional(),
        ticket_description: Joi.string()
          .min(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MIN)
          .max(VALIDATION_CONSTANT.TICKET_DESCRIPTION_MAX)
          .optional(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .optional(),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .optional(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .optional(),
        ticket_status: Joi.string()
          .valid(...Object.values(TICKET_STATUS))
          .optional(),
        assigned_to_user_id: Joi.number().integer().positive().optional(),
        resolution_note: Joi.string()
          .min(10)
          .max(VALIDATION_CONSTANT.RESOLUTION_NOTE_MAX_LENGTH)
          .optional(),
        rating: Joi.number()
          .integer()
          .min(VALIDATION_CONSTANT.RATING_MIN)
          .max(VALIDATION_CONSTANT.RATING_MAX)
          .optional(),
        review_comment: Joi.string()
          .max(VALIDATION_CONSTANT.REVIEW_COMMENT_MAX_LENGTH)
          .optional(),
        // PIN verification for updates
        support_pin: Joi.string().min(1).max(50).optional(),
        // Followers management
        followers: Joi.array()
          .items(Joi.number().integer().positive())
          .optional(),
        // Manual date overrides (agent only)
        manual_start_date: Joi.date().optional(),
        manual_due_date: Joi.date().optional(),
        date_override_reason: Joi.string().max(255).optional(),
        // SLA pause functionality
        sla_paused: Joi.boolean().optional(),
        sla_pause_reason: Joi.string().max(255).optional(),
        // File upload fields are handled by multer middleware
        ticketFiles: Joi.any().optional(),
      })
      .unknown(true), // Allow unknown fields for file upload
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const getTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const deleteTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getTicketsListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      organization_id: Joi.string().optional(),
      ticket_status: Joi.string()
        .valid(...Object.values(TICKET_STATUS))
        .optional(),
      ticket_priority: Joi.string()
        .valid(...Object.values(TICKET_PRIORITY))
        .optional(),
      ticket_module: Joi.string()
        .valid(...Object.values(TICKET_MODULE))
        .optional(),
      ticket_type: Joi.string()
        .valid(...Object.values(TICKET_TYPE))
        .optional(),
      assigned_to_user_id: Joi.number().optional(),
      search: Joi.string().optional(),
      sort_by: Joi.string()
        .valid(...Object.values(SORT_BY))
        .optional(),
      sort_order: Joi.string()
        .valid(...Object.values(SORT_ORDER))
        .optional(),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    }),
  });

const assignTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      assigned_to_user_id: Joi.number().required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const resolveTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      resolution_note: Joi.string()
        .min(10)
        .max(VALIDATION_CONSTANT.RESOLUTION_NOTE_MAX_LENGTH)
        .required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const rateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      rating: Joi.number()
        .integer()
        .min(VALIDATION_CONSTANT.RATING_MIN)
        .max(VALIDATION_CONSTANT.RATING_MAX)
        .required(),
      review_comment: Joi.string()
        .max(VALIDATION_CONSTANT.REVIEW_COMMENT_MAX_LENGTH)
        .optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const addTicketCommentValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        comment_text: Joi.string()
          .min(1)
          .max(VALIDATION_CONSTANT.MESSAGE_CONTENT_MAX)
          .required(),
        is_private: Joi.boolean().default(false),
      })
      .unknown(true), // Allow file upload fields
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const getTicketCommentsValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
    [Segments.QUERY]: Joi.object()
      .keys({
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
      })
      .unknown(true),
  });

const getTicketHistoryValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

export default {
  createTicketValidator,
  updateTicketValidator,
  getTicketValidator,
  deleteTicketValidator,
  getTicketsListValidator,
  assignTicketValidator,
  resolveTicketValidator,
  rateTicketValidator,
  addTicketCommentValidator,
  getTicketCommentsValidator,
  getTicketHistoryValidator,
};
