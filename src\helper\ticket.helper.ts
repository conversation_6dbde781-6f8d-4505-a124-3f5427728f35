import { QueryTypes } from "sequelize";
import { sequelize, db } from "../models";
import { TICKET_PRIORITY } from "./constant";

/**
 * Get organization name from Keycloak groups
 */
export const getOrganizationName = async (
  organizationId: string
): Promise<string> => {
  try {
    if (!organizationId) return organizationId;

    const query = `
      SELECT kg.NAME as group_name
      FROM KEYCLOAK_GROUP kg
      WHERE kg.NAME LIKE :organizationPattern
      LIMIT 1
    `;

    const result = (await sequelize.query(query, {
      replacements: {
        organizationPattern: `%${organizationId}%`,
      },
      type: QueryTypes.SELECT,
    })) as any[];

    if (result && result.length > 0) {
      return result[0].group_name || organizationId;
    }

    return organizationId;
  } catch (error) {
    console.error("Error getting organization name:", error);
    return organizationId;
  }
};

/**
 * Get ticket by ID using raw query with user details (following recipe-ms pattern)
 * @param ticketId - Ticket ID
 * @param organizationId - Organization ID for filtering
 * @returns Complete ticket data with user information
 */
export const getTicketByIdRaw = async (
  ticketId: number,
  organizationId?: string | null
): Promise<any> => {
  try {
    const baseUrl = global.config?.API_BASE_URL || "";

    const query = `
      SELECT
        t.*,
        -- Ticket owner details from nv_users
        owner.user_email as owner_email,
        owner.user_phone_number as owner_phone,
        CONCAT(owner.user_first_name, ' ', owner.user_last_name) as owner_full_name,

        -- Assigned user details
        assigned.user_email as assigned_email,
        CONCAT(assigned.user_first_name, ' ', assigned.user_last_name) as assigned_full_name,

        -- Resolved by user details
        resolved.user_email as resolved_email,
        CONCAT(resolved.user_first_name, ' ', resolved.user_last_name) as resolved_full_name,

        -- Created by user details
        creator.user_email as creator_email,
        CONCAT(creator.user_first_name, ' ', creator.user_last_name) as creator_full_name,

        -- Rated by user details
        rater.user_email as rater_email,
        CONCAT(rater.user_first_name, ' ', rater.user_last_name) as rater_full_name
        
      FROM mo_support_tickets t
      LEFT JOIN nv_users owner ON t.ticket_owner_user_id = owner.id
      LEFT JOIN nv_users assigned ON t.assigned_to_user_id = assigned.id
      LEFT JOIN nv_users resolved ON t.resolved_by_user_id = resolved.id
      LEFT JOIN nv_users creator ON t.created_by_user_id = creator.id
      LEFT JOIN nv_users rater ON t.rated_by_user_id = rater.id
      WHERE t.id = :ticketId
        ${organizationId ? "AND t.organization_id = :organizationId" : ""}
        AND t.deleted_at IS NULL
    `;

    const replacements: any = { ticketId };
    if (organizationId) {
      replacements.organizationId = organizationId;
    }

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements,
    });

    if (!result || result.length === 0) {
      return null;
    }

    const ticket = result[0];

    // Recalculate SLA due date based on current configuration
    try {
      const orgConfig = await db.SupportConfig.findOne({
        where: { organization_id: organizationId },
      });
      ticket.sla_due_date = calculateSlaDueDate(
        ticket.created_at,
        ticket.ticket_priority,
        orgConfig
      );
    } catch (slaErr) {
      console.error("SLA recalculation error:", slaErr);
    }

    // Get attachments for this ticket
    const attachments = await getTicketAttachments(ticket.id);
    const comments = await getTicketCommentsRaw(ticket.id);

    // Get organization name from Keycloak
    const organizationName = await getOrganizationName(ticket.organization_id);

    return {
      ...ticket,
      organization_name: organizationName,
      attachments,
      comments,
    };
  } catch (error) {
    console.error("Error in getTicketByIdRaw:", error);
    throw error;
  }
};

/**
 * Get ticket by slug using raw query with user details (following recipe-ms pattern)
 * @param ticketSlug - Ticket slug
 * @param organizationId - Organization ID for filtering
 * @returns Complete ticket data with user information
 */
export const getTicketBySlugRaw = async (
  ticketSlug: string,
  organizationId?: string | null
): Promise<any> => {
  try {
    const query = `
      SELECT
        t.*,
        -- Ticket owner details from nv_users
        owner.user_email as owner_email,
        owner.user_phone_number as owner_phone,
        CONCAT(owner.user_first_name, ' ', owner.user_last_name) as owner_full_name,

        -- Assigned user details
        assigned.user_email as assigned_email,
        CONCAT(assigned.user_first_name, ' ', assigned.user_last_name) as assigned_full_name,

        -- Resolved by user details
        resolved.user_email as resolved_email,
        CONCAT(resolved.user_first_name, ' ', resolved.user_last_name) as resolved_full_name,

        -- Created by user details
        creator.user_email as creator_email,
        CONCAT(creator.user_first_name, ' ', creator.user_last_name) as creator_full_name,

        -- Rated by user details
        rater.user_email as rater_email,
        CONCAT(rater.user_first_name, ' ', rater.user_last_name) as rater_full_name

      FROM mo_support_tickets t
      LEFT JOIN nv_users owner ON t.ticket_owner_user_id = owner.id
      LEFT JOIN nv_users assigned ON t.assigned_to_user_id = assigned.id
      LEFT JOIN nv_users resolved ON t.resolved_by_user_id = resolved.id
      LEFT JOIN nv_users creator ON t.created_by_user_id = creator.id
      LEFT JOIN nv_users rater ON t.rated_by_user_id = rater.id
      WHERE t.ticket_slug = :ticketSlug
        ${organizationId ? "AND t.organization_id = :organizationId" : ""}
        AND t.deleted_at IS NULL
    `;

    const replacements: any = { ticketSlug };
    if (organizationId) {
      replacements.organizationId = organizationId;
    }

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements,
    });

    if (!result || result.length === 0) {
      return null;
    }

    const ticket = result[0];

    // Recalculate SLA due date with latest config
    try {
      const orgConfig = await db.SupportConfig.findOne({
        where: { organization_id: organizationId },
      });
      ticket.sla_due_date = calculateSlaDueDate(
        ticket.created_at,
        ticket.ticket_priority,
        orgConfig
      );
    } catch (slaErr) {
      console.error("SLA recalculation error:", slaErr);
    }

    // Get attachments for this ticket
    const attachments = await getTicketAttachments(ticket.id);
    const comments = await getTicketCommentsRaw(ticket.id);

    return {
      ...ticket,
      attachments,
      comments,
    };
  } catch (error) {
    console.error("Error in getTicketBySlugRaw:", error);
    throw error;
  }
};

/**
 * Get tickets list using raw query with pagination (following recipe-ms pattern)
 * @param organizationId - Organization ID for filtering
 * @param options - Query options (limit, offset, search, filters)
 * @returns Paginated tickets list with user information
 */
export const getTicketsListRaw = async (
  organizationId: string | undefined,
  options: {
    limit?: number;
    offset?: number;
    search?: string;
    ticket_status?: string;
    ticket_priority?: string;
    ticket_type?: string;
    ticket_module?: string;
    assigned_to_user_id?: number;
    sort_by?: string;
    sort_order?: string;
  } = {}
): Promise<{ tickets: any[]; total: number }> => {
  try {
    const {
      limit,
      offset,
      search,
      ticket_status,
      ticket_priority,
      ticket_type,
      ticket_module,
      assigned_to_user_id,
      sort_by = "created_at",
      sort_order = "DESC",
    } = options;

    // Build WHERE conditions. Always exclude deleted tickets. Apply organization filter only if provided
    const whereConditions = ["t.deleted_at IS NULL"];
    const replacements: any = {};

    if (organizationId) {
      whereConditions.push("t.organization_id = :organizationId");
      replacements.organizationId = organizationId;
    }

    if (search) {
      whereConditions.push(
        "(t.ticket_title LIKE :search OR t.ticket_description LIKE :search OR t.ticket_slug LIKE :search)"
      );
      replacements.search = `%${search}%`;
    }

    if (ticket_status) {
      whereConditions.push("t.ticket_status = :ticket_status");
      replacements.ticket_status = ticket_status;
    }

    if (ticket_priority) {
      whereConditions.push("t.ticket_priority = :ticket_priority");
      replacements.ticket_priority = ticket_priority;
    }

    if (ticket_type) {
      whereConditions.push("t.ticket_type = :ticket_type");
      replacements.ticket_type = ticket_type;
    }

    if (ticket_module) {
      whereConditions.push("t.ticket_module = :ticket_module");
      replacements.ticket_module = ticket_module;
    }

    if (assigned_to_user_id) {
      whereConditions.push("t.assigned_to_user_id = :assigned_to_user_id");
      replacements.assigned_to_user_id = assigned_to_user_id;
    }

    // Build ORDER BY clause
    const validSortFields = [
      "created_at",
      "updated_at",
      "ticket_priority",
      "ticket_status",
      "assigned_at",
      "resolved_at",
    ];
    const validSortOrders = ["ASC", "DESC"];

    const sortField = validSortFields.includes(sort_by)
      ? sort_by
      : "created_at";
    const sortDirection = validSortOrders.includes(sort_order?.toUpperCase())
      ? sort_order.toUpperCase()
      : "DESC";

    // Main query for tickets - optimized to return only required fields
    const baseQuery = `
      SELECT
        -- Essential ticket fields only
        t.id,
        t.ticket_slug,
        t.organization_id,
        t.ticket_owner_user_id,
        t.ticket_title,
        t.ticket_description,
        t.ticket_module,
        t.ticket_type,
        t.ticket_priority,
        t.ticket_status,
        t.sla_due_date,
        t.created_by_user_id,
        t.updated_by_user_id,
        t.created_at,
        t.updated_at,

        -- Ticket owner details from nv_users
        owner.user_email as owner_email,
        owner.user_phone_number as owner_phone,
        CONCAT(owner.user_first_name, ' ', owner.user_last_name) as owner_full_name

      FROM mo_support_tickets t
      LEFT JOIN nv_users owner ON t.ticket_owner_user_id = owner.id
      WHERE ${whereConditions.join(" AND ")}
      ORDER BY t.${sortField} ${sortDirection}
    `;

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM mo_support_tickets t
      WHERE ${whereConditions.join(" AND ")}
    `;

    // Execute queries
    const [tickets, countResult] = await Promise.all([
      sequelize.query(
        limit ? `${baseQuery} LIMIT :limit OFFSET :offset` : baseQuery,
        {
          type: QueryTypes.SELECT,
          replacements: limit
            ? { ...replacements, limit, offset: offset || 0 }
            : replacements,
        }
      ),
      sequelize.query(countQuery, {
        type: QueryTypes.SELECT,
        replacements,
      }),
    ]);

    const total = (countResult[0] as any)?.total || 0;

    // Recalculate SLA due date and add organization names for each ticket
    if (tickets && tickets.length > 0) {
      let orgConfig: any = null;
      if (organizationId) {
        orgConfig = await db.SupportConfig.findOne({
          where: { organization_id: organizationId },
        });
      }
      for (const t of tickets as any[]) {
        try {
          // If list may contain multiple orgs, fetch config per ticket if not same org
          if (!organizationId) {
            orgConfig = await db.SupportConfig.findOne({
              where: { organization_id: t.organization_id },
            });
          }
          t.sla_due_date = calculateSlaDueDate(
            t.created_at,
            t.ticket_priority,
            orgConfig
          );

          // Add organization name
          t.organization_name = await getOrganizationName(t.organization_id);
        } catch (slaErr) {
          console.error("SLA recalculation error:", slaErr);
        }
      }
    }

    return {
      tickets: tickets || [],
      total: parseInt(total.toString(), 10),
    };
  } catch (error) {
    console.error("Error in getTicketsListRaw:", error);
    throw error;
  }
};

/**
 * Generate unique ticket slug
 * @param organizationId - Organization ID
 * @returns Unique ticket slug
 */
export const generateTicketSlug = async (
  organizationId: string
): Promise<string> => {
  try {
    const year = new Date().getFullYear();
    const prefix = `TKT-${year}`;

    // Get the latest ticket number for this year and organization
    const query = `
      SELECT ticket_slug 
      FROM mo_support_tickets 
      WHERE organization_id = :organizationId 
        AND ticket_slug LIKE :pattern
      ORDER BY id DESC 
      LIMIT 1
    `;

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        organizationId,
        pattern: `${prefix}-%`,
      },
    });

    let nextNumber = 1;
    if (result && result.length > 0) {
      const lastSlug = (result[0] as any).ticket_slug;
      const lastNumber = parseInt(lastSlug.split("-").pop() || "0");
      nextNumber = lastNumber + 1;
    }

    return `${prefix}-${nextNumber.toString().padStart(3, "0")}`;
  } catch (error) {
    console.error("Error generating ticket slug:", error);
    throw error;
  }
};

/**
 * Get ticket attachments with file URLs
 * @param ticketId - Ticket ID
 * @returns Array of attachment objects with download URLs
 */
export const getTicketAttachments = async (
  ticketId: number
): Promise<any[]> => {
  try {
    const baseUrl = global.config?.API_BASE_URL || "";

    const query = `
      SELECT
        ta.id,
        ta.ticket_id,
        i.item_name as file_name,
        i.item_size as file_size,
        i.item_type as file_type,
        CONCAT('${baseUrl}/', i.item_location) as download_url,
        ta.created_at as uploaded_at,
        -- Uploader details (simplified)
        CONCAT(uploader.user_first_name, ' ', uploader.user_last_name) as uploaded_by
      FROM mo_support_ticket_attachments ta
      LEFT JOIN nv_items i ON ta.item_id = i.id
      LEFT JOIN nv_users uploader ON ta.created_by = uploader.id
      WHERE ta.ticket_id = :ticketId
      ORDER BY ta.created_at ASC
    `;

    const attachments = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: { ticketId },
    });

    return attachments || [];
  } catch (error) {
    console.error("Error in getTicketAttachments:", error);
    return [];
  }
};

export const getTicketCommentsRaw = async (
  ticketId: number,
  includePrivate: boolean = false
): Promise<any[]> => {
  try {
    const query = `
      SELECT
        id,
        ticket_id,
        message_text AS comment_text,
        is_private,
        attachment_id,
        created_by,
        created_at
      FROM mo_support_ticket_messages
      WHERE ticket_id = :ticketId
        AND message_type = 'TICKET_COMMENT'
        ${includePrivate ? "" : "AND is_private = false"}
      ORDER BY created_at ASC
    `;

    const comments = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: { ticketId },
    });

    return comments;
  } catch (error) {
    console.error("Error in getTicketCommentsRaw:", error);
    return [];
  }
};

/**
 * Calculate SLA due date based on ticket priority and support configuration.
 *
 * Order of precedence for SLA (in hours):
 *   1. Priority-specific config (sla_[priority]_priority_hours)
 *   2. Organization-level config (sla_resolution_time_hours)
 *   3. Fallback mapping by priority (hard-coded)
 *   4. Global default (72h)
 */
export const calculateSlaDueDate = (
  createdAt: Date,
  priority: string,
  orgConfig?: {
    sla_resolution_time_hours?: number;
    sla_low_priority_hours?: number;
    sla_medium_priority_hours?: number;
    sla_high_priority_hours?: number;
    sla_urgent_priority_hours?: number;
  }
): Date => {
  const fallbackByPriority: Record<string, number> = {
    [TICKET_PRIORITY.LOW]: 120, // 5 days
    [TICKET_PRIORITY.MEDIUM]: 72, // 3 days
    [TICKET_PRIORITY.HIGH]: 48, // 2 days
    [TICKET_PRIORITY.URGENT]: 24, // 1 day
  };

  let hours = 72; // global default

  // 1. Check for priority-specific configuration first
  if (orgConfig && priority) {
    switch (priority) {
      case TICKET_PRIORITY.LOW:
        if (orgConfig.sla_low_priority_hours) {
          hours = orgConfig.sla_low_priority_hours;
          break;
        }
        break;
      case TICKET_PRIORITY.MEDIUM:
        if (orgConfig.sla_medium_priority_hours) {
          hours = orgConfig.sla_medium_priority_hours;
          break;
        }
        break;
      case TICKET_PRIORITY.HIGH:
        if (orgConfig.sla_high_priority_hours) {
          hours = orgConfig.sla_high_priority_hours;
          break;
        }
        break;
      case TICKET_PRIORITY.URGENT:
        if (orgConfig.sla_urgent_priority_hours) {
          hours = orgConfig.sla_urgent_priority_hours;
          break;
        }
        break;
    }
  }

  // 2. Fallback to general organization config
  if (hours === 72 && orgConfig?.sla_resolution_time_hours) {
    hours = orgConfig.sla_resolution_time_hours;
  }

  // 3. Fallback to hard-coded priority mapping
  if (hours === 72 && priority && fallbackByPriority[priority]) {
    hours = fallbackByPriority[priority];
  }

  const dueDate = new Date(createdAt);
  dueDate.setHours(dueDate.getHours() + hours);
  return dueDate;
};
